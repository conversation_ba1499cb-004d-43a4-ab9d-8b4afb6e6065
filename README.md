# Pandora ELK

一个高性能的 Go 语言 ELK 日志收集系统，专为分布式追踪日志设计。支持异步发送日志到 Kafka，可与 ELK Stack 无缝集成。

## 特性

- 🚀 **高性能异步处理** - 基于固定大小的 goroutine 池，避免资源泄漏
- 📊 **分布式追踪支持** - 内置 TraceID 和 SpanID 支持
- 🔧 **灵活的配置** - 支持多种日志级别和自定义标签
- 🛡️ **并发安全** - 线程安全的设计，支持高并发场景
- 📦 **轻量级依赖** - 最小化外部依赖，易于集成

## 快速开始

### 安装

```bash
go get code.iflytek.com/fengli16/pandora_elk
```

### 基本使用

```go
package main

import (
    "time"
    elk "code.iflytek.com/fengli16/pandora_elk"
)

func main() {
    // 创建 ELK 日志管理器
    elkManager, err := elk.NewInstance(
        "your-topic",                    // Kafka 主题
        "localhost:9092",               // Kafka broker 地址
        "info",                         // 日志级别
    )
    if err != nil {
        panic(err)
    }

    // 记录不同级别的日志
    elkManager.Info("trace-id-123",
        elk.Type("user_action"),
        elk.SpanID("span-456"),
        elk.TagString("user_id", "12345"),
        elk.TagNumber("duration_ms", 150),
        elk.Data("用户登录成功"),
    )

    elkManager.Error("trace-id-124",
        elk.Type("database_error"),
        elk.TagString("table", "users"),
        elk.Data("数据库连接失败"),
    )

    // 等待日志发送完成
    time.Sleep(time.Second)
}
```

## API 文档

### 创建实例

```go
func NewInstance(topic, brokers, level string) (*ElkLogManager, error)
```

- `topic`: Kafka 主题名称
- `brokers`: Kafka broker 地址，多个地址用逗号分隔
- `level`: 日志级别 (trace, debug, info, warn, error)

### 日志记录方法

```go
func (elk *ElkLogManager) Trace(sid string, fns ...ElkTraceFn) error
func (elk *ElkLogManager) Debug(sid string, fns ...ElkTraceFn) error
func (elk *ElkLogManager) Info(sid string, fns ...ElkTraceFn) error
func (elk *ElkLogManager) Warn(sid string, fns ...ElkTraceFn) error
func (elk *ElkLogManager) Error(sid string, fns ...ElkTraceFn) error
```

- `sid`: 追踪 ID (TraceID)
- `fns`: 可变参数，用于设置日志属性

### 日志属性设置函数

```go
// 设置 SpanID
func SpanID(spanID string) ElkTraceFn

// 设置日志类型
func Type(typein string) ElkTraceFn

// 添加字符串标签
func TagString(key, tag string) ElkTraceFn

// 添加数字标签
func TagNumber[T ConstrainNumber](key string, tag T) ElkTraceFn

// 设置日志数据内容
func Data(data any) ElkTraceFn

// 设置 Span 数据内容
func Span(span any) ElkTraceFn

// 设置主题代码
func SubjectCode(code string) ElkTraceFn
```

## 配置示例

### 使用 TOML 配置文件

```toml
[runtime_log]
file_path = "./elk_logs/runtime.json"
level = "info"
max_size_mb = 100
max_back_up = 30
max_age_day = 90
compress = false
console = false

host = "*************"
limit_array = 128
limit_string = 512
log_level = "info"
service_name = "my-service"

[kafka]
brokers = "localhost:9092,localhost:9093"
topic = "application-logs"
partitioner = "rr"  # rr, hash, random
request_maxmb = 10
gzip = false

[fix_pool]
work_num = 5
queue_num = 1024
```

### 程序化配置

```go
import (
    "code.iflytek.com/fengli16/pandora_elk/model"
    "code.iflytek.com/fengli16/pandora_elk/utils"
)

func createCustomConfig() *model.ElkModel {
    conf := &model.ElkModel{
        Host:        "*************",
        ArrayLimit:  256,
        StringLimit: 1024,
        LogLevel:    "debug",
        ServiceName: "my-custom-service",
        Kafka: model.ElkKafka{
            Brokers:      "kafka1:9092,kafka2:9092",
            Topic:        "custom-logs",
            Partitioner:  "hash",
            RequestMaxMb: 20,
            Gzip:         true,
        },
        FixPool: model.ElkFixPool{
            WorkNum:  10,
            QueueNum: 2048,
        },
    }

    // 填充默认值
    utils.FillDefaults(conf)
    return conf
}
```

## 日志级别

系统支持以下日志级别（按优先级从低到高）：

- `trace` (0) - 最详细的调试信息
- `debug` (1) - 调试信息
- `info` (2) - 一般信息
- `warn` (3) - 警告信息
- `error` (4) - 错误信息

只有级别大于等于配置级别的日志才会被记录。

## 故障排查

### 常见问题

#### 1. 连接 Kafka 失败

**症状**: 程序启动时报错 "connection refused"

**解决方案**:
- 检查 Kafka 服务是否正常运行
- 验证 broker 地址和端口是否正确
- 确认网络连接是否正常

```bash
# 测试 Kafka 连接
telnet localhost 9092
```

#### 2. 日志没有发送

**症状**: 调用日志方法但没有看到日志

**解决方案**:
- 检查日志级别配置是否正确
- 确认 Kafka 主题是否存在
- 查看是否有错误返回

```go
err := elkManager.Info("test-trace", elk.Data("test message"))
if err != nil {
    log.Printf("发送日志失败: %v", err)
}
```

#### 3. 内存使用过高

**症状**: 程序内存持续增长

**解决方案**:
- 调整 `fix_pool.queue_num` 参数，减少队列大小
- 检查 Kafka 连接是否正常，避免消息积压
- 确保及时处理错误，避免重试过多

#### 4. 性能问题

**症状**: 日志发送延迟高

**解决方案**:
- 增加 `fix_pool.work_num` 参数，提高并发处理能力
- 启用 Gzip 压缩减少网络传输
- 调整 `request_maxmb` 参数优化批量发送

### 调试模式

启用调试模式查看详细信息：

```go
// 设置日志级别为 trace
elkManager, err := elk.NewInstance("topic", "localhost:9092", "trace")
```

## 性能优化建议

1. **合理设置工作池大小**: 根据系统负载调整 `work_num` 和 `queue_num`
2. **启用压缩**: 在网络带宽有限时启用 Gzip 压缩
3. **批量发送**: 适当增加 `request_maxmb` 以支持更大的消息批次
4. **选择合适的分区策略**:
   - `rr` (轮询): 均匀分布
   - `hash`: 基于键的一致性分区
   - `random`: 随机分区

## 依赖

- [Shopify/sarama](https://github.com/Shopify/sarama) - Kafka 客户端
- [bytedance/sonic](https://github.com/bytedance/sonic) - 高性能 JSON 序列化

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的日志收集和 Kafka 发送功能
- 实现异步处理和并发安全