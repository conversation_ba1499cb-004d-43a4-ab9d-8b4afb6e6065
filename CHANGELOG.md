# 更新日志

本文档记录了 Pandora ELK 项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [1.0.0] - 2024-07-10

### 新增
- 初始版本发布
- 支持异步日志收集和 Kafka 发送功能
- 实现分布式追踪支持 (TraceID/SpanID)
- 提供多种日志级别 (trace, debug, info, warn, error)
- 支持自定义标签和数据
- 实现固定大小的 goroutine 池避免资源泄漏
- 并发安全的设计
- 完整的配置系统支持

### 修复
- 修复 Kafka 生产者资源泄漏问题
- 修复全局状态的并发安全问题
- 修复 goroutine 泄漏问题
- 修复日志级别比较逻辑错误
- 修复配置文件中的拼写错误

### 改进
- 添加完整的 godoc 文档注释
- 优化错误处理逻辑
- 移除未使用的代码和字段
- 改进代码可读性和可维护性
- 添加详细的 README 文档
- 提供配置示例和使用示例

### 文档
- 创建完整的 README.md 文档
- 添加配置示例文件
- 提供基础和高级使用示例
- 添加故障排查指南
- 创建 API 文档

## [未发布]

### 计划中的功能
- 支持更多的日志输出目标 (Elasticsearch, File 等)
- 添加日志过滤和转换功能
- 实现日志采样功能
- 添加指标监控和健康检查
- 支持配置热重载
