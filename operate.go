package elk

import (
	"errors"
	"sync"

	"code.iflytek.com/fengli16/pandora_elk/collector"
	"code.iflytek.com/fengli16/pandora_elk/model"
	"code.iflytek.com/fengli16/pandora_elk/record"
	"code.iflytek.com/fengli16/pandora_elk/utils"
)

// ElkLogManager 管理 ELK 日志收集的核心结构
// 提供分布式追踪日志的收集和发送功能
type ElkLogManager struct {
	Conf *model.ElkModel      // 配置信息
	Tags map[string]string    // 全局标签
	cs   collector.Collectors // 收集器链
}

var (
	gInit     = false
	gInitLock sync.Mutex
)

func initElklog(elkConfig *model.ElkModel) (*ElkLogManager, error) {
	gInitLock.Lock()
	defer gInitLock.Unlock()

	if gInit {
		return nil, errors.New("elk log can not init twice")
	}
	gInit = true

	cs, err := collector.CreateCollectorChains(elkConfig)

	if err != nil {
		return nil, err
	}
	em := &ElkLogManager{
		Conf: elkConfig,
		Tags: map[string]string{},
		cs:   cs,
	}

	return em, nil
}

// Trace 记录 trace 级别的日志
func (elk *ElkLogManager) Trace(sid string, fns ...ElkTraceFn) error {
	return elk.write(sid, utils.Trace, fns...)
}

// Debug 记录 debug 级别的日志
func (elk *ElkLogManager) Debug(sid string, fns ...ElkTraceFn) error {
	if !utils.LevelCmp(utils.Debug, utils.ElkLevel(elk.Conf.LogLevel)) {
		return nil
	}
	return elk.write(sid, utils.Debug, fns...)
}

// Info 记录 info 级别的日志
func (elk *ElkLogManager) Info(sid string, fns ...ElkTraceFn) error {
	if !utils.LevelCmp(utils.Info, utils.ElkLevel(elk.Conf.LogLevel)) {
		return nil
	}
	return elk.write(sid, utils.Info, fns...)
}

// Warn 记录 warn 级别的日志
func (elk *ElkLogManager) Warn(sid string, fns ...ElkTraceFn) error {
	if !utils.LevelCmp(utils.Warn, utils.ElkLevel(elk.Conf.LogLevel)) {
		return nil
	}
	return elk.write(sid, utils.Warn, fns...)
}

// Error 记录 error 级别的日志
func (elk *ElkLogManager) Error(sid string, fns ...ElkTraceFn) error {
	return elk.write(sid, utils.Error, fns...)
}

func (elk *ElkLogManager) write(sid string, level utils.ElkLevel, fns ...ElkTraceFn) error {

	record := record.NewTraceRecord(elk.Conf, elk.Tags)

	record.Wrapper.TraceId = sid
	record.Wrapper.Level = level.String()

	for _, fn := range fns {
		fn(record)
	}

	return elk.cs.Send(level, record)
}

// ElkTraceFn 定义日志追踪函数类型，用于设置日志记录的各种属性
type ElkTraceFn func(tm *record.TraceRecord)

// SpanID 设置 span ID
func SpanID(spanID string) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		tm.Wrapper.SpanId = spanID
	}
}

// TagString 添加字符串类型的标签
func TagString(key, tag string) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		if tm.Wrapper.Tags == nil {
			tm.Wrapper.Tags = map[string]any{}
		}

		tm.Wrapper.Tags[key] = tag
	}
}

// ConstrainNumber 定义支持的数字类型约束
type ConstrainNumber interface {
	int | int8 | int16 | int32 | int64 | uint | uint8 | uint16 | uint32 | uint64 | float32 | float64
}

// TagNumber 添加数字类型的标签
func TagNumber[T ConstrainNumber](key string, tag T) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		if tm.Wrapper.Tags == nil {
			tm.Wrapper.Tags = map[string]any{}
		}

		tm.Wrapper.Tags[key] = tag
	}
}

// Type 设置日志类型
func Type(typein string) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		tm.Wrapper.Type = typein
	}
}

// SubjectCode 设置主题代码
func SubjectCode(code string) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		tm.Wrapper.SubjectCode = code
	}
}

// Data 设置日志数据内容
func Data(data any) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		tm.Wrapper.MesgData.Msg = data
	}
}

// Span 设置 span 数据内容
func Span(span any) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		tm.Wrapper.MesgSpan.Msg = span
	}
}
