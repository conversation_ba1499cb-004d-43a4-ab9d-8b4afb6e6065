package elk

import (
	"errors"

	"code.iflytek.com/fengli16/pandora_elk/collector"
	"code.iflytek.com/fengli16/pandora_elk/model"
	"code.iflytek.com/fengli16/pandora_elk/record"
	"code.iflytek.com/fengli16/pandora_elk/utils"
)

type ElkLogManager struct {
	Conf *model.ElkModel
	Tags map[string]string
	cs   collector.Collectors
}

var gInit = false

func initElklog(elkConfig *model.ElkModel) (*ElkLogManager, error) {

	if gInit == true {
		return nil, errors.New("elk log can not init twice")
	}
	gInit = true

	cs, err := collector.CreateCollectorChains(elkConfig)

	if err != nil {
		return nil, err
	}
	em := &ElkLogManager{
		Conf: elkConfig,
		Tags: map[string]string{},
		cs:   cs,
	}

	return em, nil
}

func (elk *ElkLogManager) Trace(sid string, fns ...ElkTraceFn) error {
	return elk.write(sid, utils.Trace, fns...)
}

func (elk *ElkLogManager) Debug(sid string, fns ...ElkTraceFn) error {
	if !utils.LevelCmp(utils.Debug, utils.ElkLevel(elk.Conf.LogLevel)) {
		return nil
	}
	return elk.write(sid, utils.Debug, fns...)
}

func (elk *ElkLogManager) Info(sid string, fns ...ElkTraceFn) error {
	if !utils.LevelCmp(utils.Info, utils.ElkLevel(elk.Conf.LogLevel)) {
		return nil
	}
	return elk.write(sid, utils.Info, fns...)
}

func (elk *ElkLogManager) Warn(sid string, fns ...ElkTraceFn) error {
	if !utils.LevelCmp(utils.Warn, utils.ElkLevel(elk.Conf.LogLevel)) {
		return nil
	}

	return elk.write(sid, utils.Warn, fns...)
}

func (elk *ElkLogManager) Error(sid string, fns ...ElkTraceFn) error {
	return elk.write(sid, utils.Error, fns...)
}

func (elk *ElkLogManager) write(sid string, level utils.ElkLevel, fns ...ElkTraceFn) error {

	record := record.NewTraceRecord(elk.Conf, elk.Tags)

	record.Wrapper.TraceId = sid
	record.Wrapper.Level = level.String()

	for _, fn := range fns {
		fn(record)
	}

	return elk.cs.Send(level, record)
}

type ElkTraceFn func(tm *record.TraceRecord)

func SpanId(spanId string) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		tm.Wrapper.SpanId = spanId
	}
}

func TagString(key, tag string) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		if tm.Wrapper.Tags == nil {
			tm.Wrapper.Tags = map[string]any{}
		}

		tm.Wrapper.Tags[key] = tag
	}
}

type ConstrainNumber interface {
	int | int8 | int16 | int32 | int64 | uint | uint8 | uint16 | uint32 | uint64 | float32 | float64
}

func TagNumber[T ConstrainNumber](key string, tag T) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		if tm.Wrapper.Tags == nil {
			tm.Wrapper.Tags = map[string]any{}
		}

		tm.Wrapper.Tags[key] = tag
	}
}

func Type(typein string) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		tm.Wrapper.Type = typein
	}
}

func SubjectCode(code string) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		tm.Wrapper.SubjectCode = code
	}
}

func Data(data any) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		tm.Wrapper.MesgData.Msg = data
	}
}

func Span(span any) ElkTraceFn {
	return func(tm *record.TraceRecord) {
		tm.Wrapper.MesgSpan.Msg = span
	}
}
