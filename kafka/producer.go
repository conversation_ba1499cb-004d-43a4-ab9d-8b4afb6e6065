package kafka

import (
	"errors"
	"strings"

	"github.com/Shopify/sarama"
)

type ProducerHandler struct {
	Topic       string
	Brokers     []string
	Partitioner string
	Sp          sarama.SyncProducer
	Asp         sarama.AsyncProducer
}

func NewProducerInstance(option *KafkaProducerModel) (*ProducerHandler, error) {
	return createProducer(option)
}

func createProducer(pc *KafkaProducerModel) (*ProducerHandler, error) {
	ph := ProducerHandler{}

	// Kafka broker 地址
	ph.Brokers = strings.Split(pc.Brokers, ",")
	ph.Topic = pc.Topic
	ph.Partitioner = pc.Partitioner

	// 创建 Sarama 配置
	config := sarama.NewConfig()
	config.Producer.RequiredAcks = sarama.WaitForAll // 等待 leader 和 follower 确认消息
	config.Producer.Retry.Max = 5                    // 设置发送失败后的最大重试次数
	config.Producer.Return.Successes = true          // 成功交付的消息会返回成功的通知

	if pc.Gzip {
		config.Producer.Compression = sarama.CompressionGZIP
	}

	if pc.RequestMaxMb != 0 {
		config.Producer.MaxMessageBytes = pc.RequestMaxMb * 1024 * 1024
	}

	switch ph.Partitioner {
	case "rr":
		config.Producer.Partitioner = sarama.NewRoundRobinPartitioner //轮询策略
	case "random":
		config.Producer.Partitioner = sarama.NewRandomPartitioner //随机
	case "hash":
		config.Producer.Partitioner = sarama.NewHashPartitioner
	default:
		return nil, errors.New("error partitioner config")
	}

	// 创建一个同步生产者
	sync_producer, err := sarama.NewSyncProducer(ph.Brokers, config)
	if err != nil {
		return nil, err
	}

	ph.Sp = sync_producer

	async_producer, err := sarama.NewAsyncProducer(ph.Brokers, config)
	if err != nil {
		return nil, err
	}
	ph.Asp = async_producer

	return &ph, nil
}

func (ph *ProducerHandler) SyncMessage(in []byte, header []sarama.RecordHeader, meta any) (partition int32, offset int64, err error) {
	// 准备发送的消息
	message := &sarama.ProducerMessage{
		Topic:    ph.Topic,               // 发送到的主题
		Value:    sarama.ByteEncoder(in), // 消息内容
		Headers:  header,
		Metadata: meta,
	}

	// 发送消息
	return ph.Sp.SendMessage(message)
}

func (ph *ProducerHandler) AsyncMessage(in []byte, header []sarama.RecordHeader, meta any) (partition int32, offset int64, err error) {
	// 准备发送的消息
	message := &sarama.ProducerMessage{
		Topic:    ph.Topic,               // 发送到的主题
		Value:    sarama.ByteEncoder(in), // 消息内容
		Headers:  header,
		Metadata: meta,
	}

	ph.Asp.Input() <- message
	return
}

// Close 关闭生产者连接，释放资源
func (ph *ProducerHandler) Close() error {
	var err error
	if ph.Sp != nil {
		if closeErr := ph.Sp.Close(); closeErr != nil {
			err = closeErr
		}
	}
	if ph.Asp != nil {
		if closeErr := ph.Asp.Close(); closeErr != nil {
			if err != nil {
				err = errors.Join(err, closeErr)
			} else {
				err = closeErr
			}
		}
	}
	return err
}
