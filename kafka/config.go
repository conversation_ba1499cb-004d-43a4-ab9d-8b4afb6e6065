package kafka

import (
	"errors"
)

type KafkaConsumerModel struct {
	Brokers      string `json:"brokers" toml:"brokers"`
	Group        string `json:"group" toml:"group"`
	Topics       string `json:"topics" toml:"topics"`
	Offset       string `json:"offset" toml:"offset" default:"oldest"`
	AutoCommit   bool   `json:"auto_commit" toml:"auto_commit" default:"true"`
	FetchDefault int    `json:"fetch_default" toml:"fetch_default" default:"1"` //mb
}

type KafkaProducerModel struct {
	Brokers      string `json:"brokers" toml:"brokers"`
	Topic        string `json:"topic" toml:"topic"`
	Partitioner  string `json:"partitioner" toml:"partitioner" default:"rr"`
	RequestMaxMb int    `json:"request_maxmb" toml:"request_maxmb" default:"10"`
	Gzip         bool   `json:"gzip" toml:"gzip" default:"false"`
}

func (config KafkaConsumerModel) Check() error {
	if config.Brokers == "" {
		return errors.New("empity brokers")
	}

	if config.Group == "" {
		return errors.New("empity group")
	}

	if config.Topics == "" {
		return errors.New("empity topics")
	}

	if config.Offset != "newest" && config.Offset != "oldest" {
		return errors.New("invalid Offset")
	}

	if config.FetchDefault == 0 {
		return errors.New("invalid fetch default")
	}

	return nil
}

func (config KafkaProducerModel) Check() error {
	if config.Topic == "" {
		return errors.New("empity topic")
	}

	if config.Brokers == "" {
		return errors.New("empity brokers")
	}

	if config.Partitioner != "rr" && config.Partitioner != "hash" && config.Partitioner != "random" {
		return errors.New("invalid partitioner")
	}

	if config.RequestMaxMb == 0 {
		return errors.New("invalid request_maxmb")
	}

	return nil
}
