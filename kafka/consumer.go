package kafka

import (
	"context"
	"errors"

	"github.com/Shopify/sarama"
)

type ProcessorFn func(session sarama.ConsumerGroupSession, mesg *sarama.ConsumerMessage)

type ConsumerGroupHandler struct {
	Group         string
	Topics        []string
	Brokers       []string
	Offset        int64
	prossor       ProcessorFn
	consumerGroup sarama.ConsumerGroup
	context       context.Context
	cancel        context.CancelFunc
}

func (ConsumerGroupHandler) Setup(_ sarama.ConsumerGroupSession) error {
	return nil
}

func (ConsumerGroupHandler) Cleanup(_ sarama.ConsumerGroupSession) error {
	return nil
}

func (h ConsumerGroupHandler) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for message := range claim.Messages() {
		h.prossor(session, message)
	}

	return nil
}

func (ch *ConsumerGroupHandler) Close() {
	if ch.cancel != nil {
		ch.cancel()
	}
	ch.consumerGroup.Close()
}

func (ch *ConsumerGroupHandler) SetProcessor(fn ProcessorFn) {
	ch.prossor = fn
}

func (ch *ConsumerGroupHandler) Run() error {
	if ch.context == nil {
		ctx, cancel := context.WithCancel(context.TODO())
		ch.context = ctx
		ch.cancel = cancel
	}

	defer ch.Close()

	for {
		if err := ch.consumerGroup.Consume(ch.context, ch.Topics, ch); err != nil {
			return err
		}

		if ch.context.Err() != nil {
			return errors.New("context cancel")
		}
	}
}

func (ch *ConsumerGroupHandler) RunWithContext(pctx context.Context) error {
	ctx, cancel := context.WithCancel(pctx)
	ch.context = ctx
	ch.cancel = cancel

	defer ch.Close()

	for {
		if err := ch.consumerGroup.Consume(ch.context, ch.Topics, ch); err != nil {
			return err
		}

		if ctx.Err() != nil {
			return errors.New("context cancel")
		}
	}
}

func (ch *ConsumerGroupHandler) Context() context.Context {
	return ch.context
}

func (ch *ConsumerGroupHandler) SetContxt(pctx context.Context) context.Context {
	ctx, cancel := context.WithCancel(pctx)
	ch.context = ctx
	ch.cancel = cancel
	return ch.context
}
