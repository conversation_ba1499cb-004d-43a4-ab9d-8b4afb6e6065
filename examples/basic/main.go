package main

import (
	"fmt"
	"log"
	"time"

	elk "code.iflytek.com/fengli16/pandora_elk"
)

func main() {
	// 创建 ELK 日志管理器
	elkManager, err := elk.NewInstance(
		"application-logs",              // Kafka 主题
		"localhost:9092",               // Kafka broker 地址
		"debug",                        // 日志级别
	)
	if err != nil {
		log.Fatalf("创建 ELK 管理器失败: %v", err)
	}

	fmt.Println("开始发送日志...")

	// 示例 1: 用户登录日志
	err = elkManager.Info("trace-001", 
		elk.Type("user_login"),
		elk.SpanID("span-001"),
		elk.TagString("user_id", "12345"),
		elk.TagString("ip", "*************"),
		elk.TagNumber("login_time", time.Now().Unix()),
		elk.Data(map[string]interface{}{
			"action": "login",
			"result": "success",
			"method": "password",
		}),
	)
	if err != nil {
		log.Printf("发送登录日志失败: %v", err)
	}

	// 示例 2: API 请求日志
	err = elkManager.Debug("trace-002",
		elk.Type("api_request"),
		elk.SpanID("span-002"),
		elk.TagString("endpoint", "/api/users"),
		elk.TagString("method", "GET"),
		elk.TagNumber("status_code", 200),
		elk.TagNumber("duration_ms", 45),
		elk.Data("获取用户列表"),
		elk.Span(map[string]interface{}{
			"request_id": "req-12345",
			"user_agent": "Mozilla/5.0",
		}),
	)
	if err != nil {
		log.Printf("发送 API 请求日志失败: %v", err)
	}

	// 示例 3: 数据库操作日志
	err = elkManager.Warn("trace-003",
		elk.Type("database_slow_query"),
		elk.SpanID("span-003"),
		elk.TagString("table", "users"),
		elk.TagString("operation", "SELECT"),
		elk.TagNumber("duration_ms", 2500),
		elk.SubjectCode("DB001"),
		elk.Data("查询执行时间过长"),
	)
	if err != nil {
		log.Printf("发送数据库日志失败: %v", err)
	}

	// 示例 4: 错误日志
	err = elkManager.Error("trace-004",
		elk.Type("system_error"),
		elk.SpanID("span-004"),
		elk.TagString("component", "payment_service"),
		elk.TagString("error_code", "PAY_001"),
		elk.Data(map[string]interface{}{
			"error": "支付网关连接超时",
			"retry_count": 3,
			"last_attempt": time.Now().Format(time.RFC3339),
		}),
	)
	if err != nil {
		log.Printf("发送错误日志失败: %v", err)
	}

	// 示例 5: 性能监控日志
	err = elkManager.Info("trace-005",
		elk.Type("performance_metric"),
		elk.SpanID("span-005"),
		elk.TagString("service", "user_service"),
		elk.TagNumber("cpu_usage", 75.5),
		elk.TagNumber("memory_usage", 1024),
		elk.TagNumber("active_connections", 150),
		elk.Data("系统性能指标"),
	)
	if err != nil {
		log.Printf("发送性能日志失败: %v", err)
	}

	fmt.Println("日志发送完成，等待处理...")
	
	// 等待异步处理完成
	time.Sleep(3 * time.Second)
	
	fmt.Println("示例程序执行完成")
}
