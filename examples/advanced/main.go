package main

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"sync"
	"time"

	elk "code.iflytek.com/fengli16/pandora_elk"
)

// 模拟用户请求处理
func handleUserRequest(elkManager *elk.ElkLogManager, userID string, requestID string) {
	traceID := fmt.Sprintf("trace-%s", requestID)
	
	// 开始处理请求
	start := time.Now()
	
	elkManager.Info(traceID,
		elk.Type("request_start"),
		elk.SpanID("span-start"),
		elk.TagString("user_id", userID),
		elk.TagString("request_id", requestID),
		elk.Data("开始处理用户请求"),
	)
	
	// 模拟数据库查询
	dbStart := time.Now()
	time.Sleep(time.Duration(rand.Intn(100)) * time.Millisecond)
	dbDuration := time.Since(dbStart)
	
	elkManager.Debug(traceID,
		elk.Type("database_query"),
		elk.SpanID("span-db"),
		elk.TagString("user_id", userID),
		elk.TagString("query", "SELECT * FROM users WHERE id = ?"),
		elk.TagNumber("duration_ms", dbDuration.Milliseconds()),
		elk.Data("数据库查询完成"),
	)
	
	// 模拟业务逻辑处理
	businessStart := time.Now()
	time.Sleep(time.Duration(rand.Intn(50)) * time.Millisecond)
	businessDuration := time.Since(businessStart)
	
	elkManager.Debug(traceID,
		elk.Type("business_logic"),
		elk.SpanID("span-business"),
		elk.TagString("user_id", userID),
		elk.TagNumber("duration_ms", businessDuration.Milliseconds()),
		elk.Data("业务逻辑处理完成"),
	)
	
	// 随机产生一些错误
	if rand.Float32() < 0.1 { // 10% 的错误率
		elkManager.Error(traceID,
			elk.Type("processing_error"),
			elk.SpanID("span-error"),
			elk.TagString("user_id", userID),
			elk.TagString("error_type", "validation_failed"),
			elk.Data("用户输入验证失败"),
		)
		return
	}
	
	// 完成请求处理
	totalDuration := time.Since(start)
	
	elkManager.Info(traceID,
		elk.Type("request_complete"),
		elk.SpanID("span-complete"),
		elk.TagString("user_id", userID),
		elk.TagString("request_id", requestID),
		elk.TagNumber("total_duration_ms", totalDuration.Milliseconds()),
		elk.TagNumber("db_duration_ms", dbDuration.Milliseconds()),
		elk.TagNumber("business_duration_ms", businessDuration.Milliseconds()),
		elk.Data("请求处理完成"),
	)
}

// 模拟系统监控
func systemMonitor(ctx context.Context, elkManager *elk.ElkLogManager) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			// 模拟系统指标
			cpuUsage := 20 + rand.Float64()*60  // 20-80%
			memoryUsage := 30 + rand.Float64()*40 // 30-70%
			diskUsage := 10 + rand.Float64()*30   // 10-40%
			
			elkManager.Info("system-monitor",
				elk.Type("system_metrics"),
				elk.SpanID("span-metrics"),
				elk.TagNumber("cpu_usage_percent", cpuUsage),
				elk.TagNumber("memory_usage_percent", memoryUsage),
				elk.TagNumber("disk_usage_percent", diskUsage),
				elk.TagNumber("timestamp", time.Now().Unix()),
				elk.Data("系统性能指标"),
			)
			
			// 如果 CPU 使用率过高，发送警告
			if cpuUsage > 80 {
				elkManager.Warn("system-alert",
					elk.Type("system_alert"),
					elk.SpanID("span-alert"),
					elk.TagString("alert_type", "high_cpu"),
					elk.TagNumber("cpu_usage_percent", cpuUsage),
					elk.Data("CPU 使用率过高"),
				)
			}
		}
	}
}

func main() {
	// 创建 ELK 日志管理器
	elkManager, err := elk.NewInstance(
		"advanced-logs",
		"localhost:9092",
		"debug",
	)
	if err != nil {
		log.Fatalf("创建 ELK 管理器失败: %v", err)
	}
	
	fmt.Println("启动高级示例程序...")
	
	// 启动系统监控
	ctx, cancel := context.WithCancel(context.Background())
	go systemMonitor(ctx, elkManager)
	
	// 模拟并发用户请求
	var wg sync.WaitGroup
	
	// 启动多个 goroutine 模拟并发请求
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			
			for j := 0; j < 5; j++ {
				userID := fmt.Sprintf("user-%d", rand.Intn(1000))
				requestID := fmt.Sprintf("req-%d-%d", workerID, j)
				
				handleUserRequest(elkManager, userID, requestID)
				
				// 随机间隔
				time.Sleep(time.Duration(rand.Intn(1000)) * time.Millisecond)
			}
		}(i)
	}
	
	// 等待所有请求处理完成
	wg.Wait()
	
	// 停止系统监控
	cancel()
	
	fmt.Println("所有请求处理完成，等待日志发送...")
	
	// 等待日志发送完成
	time.Sleep(5 * time.Second)
	
	fmt.Println("高级示例程序执行完成")
}
