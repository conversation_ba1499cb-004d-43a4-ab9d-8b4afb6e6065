package utils

// 定义自定义类型
type ElkLevel string

const (
	Trace ElkLevel = "trace"
	Debug ElkLevel = "debug"
	Info  ElkLevel = "info"
	Warn  ElkLevel = "warn"
	Error ElkLevel = "error"
)

var g_levels = map[ElkLevel]int{
	Trace: 0,
	Debug: 1,
	Info:  2,
	Warn:  3,
	Error: 4,
}

func (c ElkLevel) String() string {
	return string(c)
}

// LevelCmp 比较日志级别，返回 currentLevel 是否应该被记录
// currentLevel: 当前日志级别
// configLevel: 配置的最低日志级别
func LevelCmp(currentLevel ElkLevel, configLevel ElkLevel) bool {
	return g_levels[currentLevel] >= g_levels[configLevel]
}
