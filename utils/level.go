package utils

// 定义自定义类型
type ElkLevel string

const (
	Trace ElkLevel = "trace"
	Debug ElkLevel = "debug"
	Info  ElkLevel = "info"
	Warn  ElkLevel = "warn"
	Error ElkLevel = "error"
)

var g_levels = map[ElkLevel]int{
	Trace: 0,
	Debug: 1,
	Info:  2,
	Warn:  3,
	Error: 4,
}

func (c ElkLevel) String() string {
	return string(c)
}

func LevelCmp(pre ElkLevel, post ElkLevel) bool {
	if g_levels[pre] >= g_levels[post] {
		return true
	}

	return false
}
