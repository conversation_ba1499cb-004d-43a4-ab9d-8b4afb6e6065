package utils

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

// 使用 default 标签填充结构体空白字段
func FillDefaults(s interface{}) error {
	// todo:现无法判断默认零值和主动设置的零值
	value := reflect.ValueOf(s)
	if value.Kind() != reflect.Ptr || value.Elem().Kind() != reflect.Struct {
		return fmt.Errorf("input must be a pointer to a struct")
	}
	// 获取指针指向的内容
	value = value.Elem()
	for i := 0; i < value.NumField(); i++ {
		field := value.Field(i)
		switch field.Kind() {
		case reflect.Struct:
			if err := FillDefaults(field.Addr().Interface()); err != nil {
				return err
			}
		case reflect.Ptr:
			// 指针类型，需要先初始化
			if field.IsNil() {
				// 此时field是指针类型，需要先获取指针指向的类型，再初始化
				field.Set(reflect.New(field.Type().Elem()))
			}
			if err := FillDefaults(field.Interface()); err != nil {
				return err
			}
		default:
			fieldType := value.Type().Field(i)
			defaultTag := fieldType.Tag.Get("default")
			// 默认零值和无default标签，不需要填充
			if defaultTag == "" || !field.IsZero() {
				continue
			}
			// 将tag中default取出并转化类型
			convertedElem, err := convertString(defaultTag, field.Type())
			if err != nil {
				return fmt.Errorf("数据类型转化失败: %v", err)
			}
			defaultValue := reflect.ValueOf(convertedElem)
			field.Set(defaultValue)
		}
	}
	return nil
}

// convertString 将字符串转换为指定类型
func convertString(value string, targetType reflect.Type) (interface{}, error) {
	switch targetType.Kind() {
	case reflect.Int:
		// 转换为 int
		intValue, parseErr := strconv.Atoi(value)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to int: %v", value, parseErr)
		}
		return intValue, nil

	case reflect.Int32:
		// 转换为 int32
		intValue, parseErr := strconv.ParseInt(value, 10, 32)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to int32: %v", value, parseErr)
		}
		return int32(intValue), nil

	case reflect.Int64:
		// 转换为 int64
		intValue, parseErr := strconv.ParseInt(value, 10, 64)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to int64: %v", value, parseErr)
		}
		return intValue, nil

	case reflect.Uint:
		// 转换为 uint
		uintValue, parseErr := strconv.ParseUint(value, 10, 32)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to uint: %v", value, parseErr)
		}
		return uint(uintValue), nil

	case reflect.Uint32:
		// 转换为 uint32
		uintValue, parseErr := strconv.ParseUint(value, 10, 32)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to uint32: %v", value, parseErr)
		}
		return uint32(uintValue), nil

	case reflect.Uint64:
		// 转换为 uint64
		uintValue, parseErr := strconv.ParseUint(value, 10, 64)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to uint64: %v", value, parseErr)
		}
		return uintValue, nil

	case reflect.Float32:
		// 转换为 float32
		floatValue, parseErr := strconv.ParseFloat(value, 32)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to float32: %v", value, parseErr)
		}
		return float32(floatValue), nil

	case reflect.Float64:
		// 转换为 float64
		floatValue, parseErr := strconv.ParseFloat(value, 64)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to float64: %v", value, parseErr)
		}
		return floatValue, nil

	case reflect.String:
		// 返回字符串本身
		return value, nil

	case reflect.Bool:
		// 转换为 bool
		boolValue, parseErr := strconv.ParseBool(value)
		if parseErr != nil {
			return nil, fmt.Errorf("failed to convert %q to bool: %v", value, parseErr)
		}
		return boolValue, nil

	case reflect.Slice:
		// 将逗号分隔的字符串转换为切片
		if targetType.Elem().Kind() == reflect.String {
			return strings.Split(value, ","), nil
		}
		sliceValues := strings.Split(value, ",")
		slice := reflect.MakeSlice(targetType, len(sliceValues), len(sliceValues))
		for i, v := range sliceValues {
			elemValue, elemErr := convertString(strings.TrimSpace(v), targetType.Elem())
			if elemErr != nil {
				return nil, fmt.Errorf("failed to convert %q to slice element: %v", v, elemErr)
			}
			slice.Index(i).Set(reflect.ValueOf(elemValue))
		}
		return slice.Interface(), nil

	default:
		// 处理不支持的类型
		return nil, fmt.Errorf("unsupported target type: %v", targetType)
	}
}
