package utils

import "sync"

type RecyclePool[T any] struct {
	sp      *sync.Pool
	newFn   func() *T
	resetFn func(*T)
}

func NewRecyclePool[T any]() *RecyclePool[T] {
	rp := &RecyclePool[T]{
		sp: &sync.Pool{},
		newFn: func() *T {
			return new(T)
		},
		resetFn: func(t *T) {},
	}

	rp.sp.New = func() any {
		return rp.newFn()
	}

	return rp
}

func (rp *RecyclePool[T]) Get() *T {
	return rp.sp.Get().(*T)
}

func (rp *RecyclePool[T]) SetNewFunc(nf func() *T) {
	rp.newFn = nf
}

func (rp *RecyclePool[T]) SetResetFunc(nf func(*T)) {
	rp.resetFn = nf
}

func (rh *RecyclePool[T]) Release(value *T) {
	rh.resetFn(value)
	rh.sp.Put(value)
}
