package elk

import (
	"testing"
	"time"

	elk "code.iflytek.com/fengli16/pandora_elk"
)

func TestElk(t *testing.T) {
	topic := "lynxiao_flow"
	brokers := "kafka-0.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092, kafka-1.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092, kafka-2.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092"

	elkManager, err := elk.NewInstance(topic, brokers, "error")
	if err != nil {
		panic(err)
	}

	elkManager.Error("feng-test", elk.Type("testelk"), elk.TagNumber("cost", 1.16), elk.TagString("test2", "yyyyy"), elk.Data("this is test"))
	time.Sleep(3 * time.Second)
}
