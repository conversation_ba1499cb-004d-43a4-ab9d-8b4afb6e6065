# Pandora ELK 配置示例文件
# 复制此文件为 config.toml 并根据需要修改配置

# 运行时日志配置
[runtime_log]
# 日志文件路径
file_path = "./elk_logs/runtime.json"
# 运行时日志级别
level = "info"
# 单个日志文件最大大小 (MB)
max_size_mb = 100
# 保留的备份文件数量
max_back_up = 30
# 日志文件保留天数
max_age_day = 90
# 是否压缩旧日志文件
compress = false
# 是否同时输出到控制台
console = false

# 主机配置
host = "*************"

# 数组和字符串长度限制
limit_array = 128
limit_string = 512

# 应用日志级别 (trace, debug, info, warn, error)
log_level = "info"

# 服务名称，用于标识日志来源
service_name = "my-application"

# Kafka 配置
[kafka]
# Kafka broker 地址，多个地址用逗号分隔
brokers = "localhost:9092,localhost:9093,localhost:9094"
# Kafka 主题名称
topic = "application-logs"
# 分区策略: rr (轮询), hash (哈希), random (随机)
partitioner = "rr"
# 单个请求最大大小 (MB)
request_maxmb = 10
# 是否启用 Gzip 压缩
gzip = false

# 异步处理池配置
[fix_pool]
# 工作 goroutine 数量
work_num = 5
# 任务队列大小
queue_num = 1024
