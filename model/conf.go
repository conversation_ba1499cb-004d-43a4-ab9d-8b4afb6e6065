package model

type ElkRuntimeLog struct {
	Fpath     string `toml:"file_path" default:"./elk_logs/runtime.json"`
	Level     string `toml:"level" default:"info"`
	MaxSizeMb int    `toml:"max_size_mb" default:"100"`
	MaxBackUp int    `toml:"max_back_up" default:"30"`
	MaxAge    int    `toml:"max_age_day" default:"90"`
	Compress  bool   `toml:"compress" default:"false"`
	Console   bool   `toml:"console" default:"false"`
}

type ElkFixPool struct {
	WorkNum  int `toml:"work_num" default:"5"`
	QueueNum int `toml:"queue_num" default:"1024"`
}

type ElkKafka struct {
	Brokers      string `json:"brokers" toml:"brokers"`
	Topic        string `json:"topic" toml:"topic"`
	Partitioner  string `json:"partitioner" toml:"partitioner" default:"rr"`
	RequestMaxMb int    `json:"request_maxmb" toml:"request_maxmb" default:"10"`
	Gzip         bool   `json:"gzip" toml:"gzip" default:"false"`
}

type ElkModel struct {
	RunTimeLog  ElkRuntimeLog `toml:"runtime_log"` //elk后台线程运行时日志记录
	Host        string        `toml:"host"`
	ArrayLimit  int           `toml:"limit_array" default:"128"`
	StringLimit int           `toml:"limit_string" default:"512"`
	LogLevel    string        `toml:"log_level" default:"info"`
	ServiceName string        `toml:"service_name"`
	Kafka       ElkKafka      `toml:"kafka"`
	FixPool     ElkFixPool    `toml:"fix_pool"`
}
