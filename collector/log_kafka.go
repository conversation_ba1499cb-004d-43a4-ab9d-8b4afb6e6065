package collector

import (
	"errors"

	"code.iflytek.com/fengli16/pandora_elk/async/fixpool"
	"code.iflytek.com/fengli16/pandora_elk/kafka"
	"code.iflytek.com/fengli16/pandora_elk/model"

	"github.com/bytedance/sonic"
)

type kafkaCollector struct {
	fp       *fixpool.AsyncFixPool
	producer *kafka.ProducerHandler
}

func newKafkaCollector(conf *model.ElkModel) (Collector, error) {

	var lo *kafka.ProducerHandler
	var err error
	lo, err = kafka.NewProducerInstance(&kafka.KafkaProducerModel{
		Brokers:      conf.Kafka.Brokers,
		Topic:        conf.Kafka.Topic,
		Partitioner:  conf.Kafka.Partitioner,
		RequestMaxMb: conf.Kafka.RequestMaxMb,
		Gzip:         conf.Kafka.Gzip,
	})

	if err != nil {
		return nil, err
	}

	var pool *fixpool.AsyncFixPool
	pool = fixpool.NewFixTasksPool(&fixpool.FixPoolModel{
		WorkNum:  conf.FixPool.WorkNum,
		QueueNum: conf.FixPool.QueueNum,
	})

	lc := &kafkaCollector{
		fp:       pool,
		producer: lo,
	}

	return lc, nil
}

func (lc *kafkaCollector) Send(lm *logMsg) error {
	if lm == nil {
		return errors.New("local collector:input log msg is nil")
	}

	_, err := lc.fp.SubmitWithoutBlock(func() (any, error) {
		s, err := sonic.Marshal(lm.data)

		if err != nil {
			return nil, err
		}

		_, _, err = lc.producer.SyncMessage(s, nil, nil)

		if err != nil {
			return nil, err
		}

		return "", nil
	})

	return err
}

// Close 关闭 kafkaCollector，释放资源
func (lc *kafkaCollector) Close() error {
	var err error
	if lc.fp != nil {
		lc.fp.Close()
	}
	if lc.producer != nil {
		if closeErr := lc.producer.Close(); closeErr != nil {
			err = closeErr
		}
	}
	return err
}
