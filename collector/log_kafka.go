package collector

import (
	"errors"

	"code.iflytek.com/fengli16/pandora_elk/async/fixpool"
	"code.iflytek.com/fengli16/pandora_elk/kafka"
	"code.iflytek.com/fengli16/pandora_elk/model"

	"github.com/bytedance/sonic"
)

type kafkaCollector struct {
	fp             *fixpool.AsyncFixPool
	producer       *kafka.ProducerHandler
	limitStringLen int
	limitArrayLen  int
}

func newKafkaColector(conf *model.ElkModel) (Collector, error) {

	var lo *kafka.ProducerHandler
	var err error
	lo, err = kafka.NewProducerInstance(&kafka.KafkaProducerModel{
		Brokers:      conf.Kafka.Brokers,
		Topic:        conf.Kafka.Topic,
		Partitioner:  conf.Kafka.Partitioner,
		RequestMaxMb: conf.Kafka.RequestMaxMb,
		Gzip:         conf.Kafka.Gzip,
	})

	if err != nil {
		return nil, err
	}

	var pool *fixpool.AsyncFixPool
	pool = fixpool.NewFixTasksPool(&fixpool.FixPoolModel{
		WorkNum:  conf.FixPool.WorkNum,
		QueueNum: conf.FixPool.QueueNum,
	})

	lc := &kafkaCollector{
		fp:             pool,
		producer:       lo,
		limitStringLen: conf.StringLimit,
		limitArrayLen:  conf.ArrayLimit,
	}

	return lc, nil
}

func (lc *kafkaCollector) Send(lm *logMsg) error {
	if lm == nil {
		return errors.New("local collector:input log msg is nil")
	}

	_, err := lc.fp.SubmitWithoutBlock(func() (any, error) {
		s, err := sonic.Marshal(lm.data)

		if err != nil {
			return nil, err
		}

		_, _, err = lc.producer.SyncMessage(s, nil, nil)

		if err != nil {
			return nil, err
		}

		return "", nil
	})

	return err
}
