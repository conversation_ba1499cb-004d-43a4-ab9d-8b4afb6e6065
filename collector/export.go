package collector

import (
	"errors"

	"code.iflytek.com/fengli16/pandora_elk/model"
	"code.iflytek.com/fengli16/pandora_elk/record"
	"code.iflytek.com/fengli16/pandora_elk/utils"

	"github.com/bytedance/sonic"
)

type Collectors []Collector

type Collector interface {
	Send(lm *logMsg) error
}

type logMsg struct {
	utils.ElkLevel
	data  *record.TraceInfo
	errmd error
	errms error
}

func CreateCollectorChains(m *model.ElkModel) (Collectors, error) {
	r := make([]Collector, 0, 2)
	var err error

	lk, err := newKafkaCollector(m)
	if err != nil {
		return nil, err
	}

	if lk != nil {
		r = append(r, lk)
	}

	if len(r) == 0 {
		return nil, errors.New("no elk log init")
	}

	return r, nil
}

func (c Collectors) Send(level utils.ElkLevel, data *record.TraceRecord) error {
	var md []byte
	var errmd error
	md, errmd = sonic.Marshal(&data.Wrapper.MesgData)

	if errmd != nil {
		md = []byte(errmd.Error())
	}

	var ms []byte
	var errms error
	ms, errms = sonic.Marshal(&data.Wrapper.MesgSpan)

	if errms != nil {
		ms = []byte(errms.Error())
	}

	var err_return error

	for _, v := range c {
		lm := &logMsg{
			ElkLevel: level,
			data:     data.TraceInfo(),
			errmd:    errmd,
			errms:    errms,
		}

		lm.data.Data = string(md)
		lm.data.Span = string(ms)

		err := v.Send(lm)

		if err != nil {
			if err_return != nil {
				err_return = errors.Join(err_return, err)
			} else {
				err_return = err
			}
		}
	}

	return err_return
}
