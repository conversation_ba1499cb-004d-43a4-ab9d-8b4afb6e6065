package feature

import "errors"

var (
	PanicError           = errors.New("goroutines pool panic")
	SubmitTimeout        = errors.New("submit task timeout")
	CtxDone              = errors.New("submit context done")
	NilFunc              = errors.New("nil func")
	ResultChanFullError  = errors.New("goroutine write result chan full error")
	ResultChanCloseError = errors.New("goroutine result chan close error")
)
