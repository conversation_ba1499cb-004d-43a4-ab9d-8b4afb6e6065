package feature

type AsyncFeatureAPI interface {
	Result() <-chan *Result //返回只读通道，该通道可能已经被关闭，不允许写
	TryResult() *Result
}

type AsyncFeature struct {
	result chan *Result

	ffn FeatureFunc
}

func NewAsyncFeature(ffn FeatureFunc) (*AsyncFeature, error) {

	if ffn == nil {
		return nil, NilFunc
	}
	return &AsyncFeature{
		result: make(chan *Result, 1),

		ffn: ffn,
	}, nil
}


// 加锁是为了保护并发
func (af *AsyncFeature) Result() <-chan *Result {
	return af.result
}

// 这一步可能读不出来值
func (af *AsyncFeature) TryResult() *Result {
	select {
	case r, ok := <-af.result:
		if ok {
			return r
		} else {
			return nil
		}
	default:
		return nil
	}
}

func (af *AsyncFeature) trySend(output any, err error) error {

	r := &Result{
		output: output,
		err:    err,
	}

	select {
	case af.result <- r:
		return nil
	default:
		return ResultChanFullError
	}

	return nil
}

func (af *AsyncFeature) send(output any, err error) {

	r := &Result{
		output: output,
		err:    err,
	}

	af.result <- r
	return
}
