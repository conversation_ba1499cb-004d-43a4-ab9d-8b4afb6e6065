package fixpool

import (
	"context"
	"errors"
	"time"

	"code.iflytek.com/fengli16/pandora_elk/async/feature"
)

type queuePool struct {
	queue    chan *feature.AsyncFeature
	workNum  int
	queueNum int
	ctx      context.Context
	cancel   context.CancelFunc
}

const defaultWorkNum = 10

func newQueuePool(workNum, queueNum int) *queuePool {

	if workNum == 0 {
		workNum = defaultWorkNum
	}

	ctx, cancel := context.WithCancel(context.TODO())

	var fp *queuePool

	if queueNum == 0 {
		fp = &queuePool{
			queue:    make(chan *feature.AsyncFeature),
			workNum:  workNum,
			queueNum: queueNum,
			ctx:      ctx,
			cancel:   cancel,
		}
	} else {
		fp = &queuePool{
			queue:    make(chan *feature.AsyncFeature, queueNum),
			workNum:  workNum,
			queueNum: queueNum,
			ctx:      ctx,
			cancel:   cancel,
		}
	}

	for i := 0; i < workNum; i++ {
		go fp.process()
	}

	return fp
}

func (fp *queuePool) Close() {
	fp.cancel()
}

func (fp *queuePool) SubmitBlock(task *feature.AsyncFeature) error {
	fp.queue <- task
	return nil
}

func (fp *queuePool) SubmitWithoutBlock(task *feature.AsyncFeature) error {
	select {
	case fp.queue <- task:
		return nil
	default:
		return errors.New("fix pool queue is full...")
	}
}

func (fp *queuePool) SubmitWithTimeOut(task *feature.AsyncFeature, timeout time.Duration) error {
	select {
	case fp.queue <- task:
		return nil
	case <-time.After(timeout):
		return feature.SubmitTimeout
	}
}

func (fp *queuePool) SubmitWithContext(ctx context.Context, task *feature.AsyncFeature) error {
	select {
	case fp.queue <- task:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}
