package fixpool

import (
	"context"
	"errors"
	"sync"
	"time"

	"code.iflytek.com/fengli16/pandora_elk/async/feature"
)

type AsyncFixPool struct {
	config      *FixPoolModel
	statusLock  sync.Mutex //避免关闭一个正在写的pool,因此加锁保护
	statusClose bool

	gp *queuePool
}

func NewFixTasksPool(m *FixPoolModel) *AsyncFixPool {

	afp := &AsyncFixPool{
		statusLock:  sync.Mutex{},
		statusClose: false,
		gp:          newQueuePool(m.WorkNum, m.QueueNum),
	}
	return afp
}

func (fp *AsyncFixPool) Close() {
	fp.close()
	fp.gp.Close()
}

func (fp *AsyncFixPool) close() {
	fp.statusLock.Lock()
	defer fp.statusLock.Unlock()

	fp.statusClose = true
}

func (fp *AsyncFixPool) SubmitBlock(fn feature.FeatureFunc) (feature.AsyncFeatureAPI, error) {
	task, err := feature.NewAsyncFeature(fn)

	if err != nil {
		return nil, err
	}

	fp.statusLock.Lock()
	defer fp.statusLock.Unlock()

	if fp.statusClose {
		return nil, errors.New("pool closed")
	}

	err = fp.gp.SubmitBlock(task)

	if err != nil {
		return nil, err
	}

	return task, nil
}

func (fp *AsyncFixPool) SubmitWithoutBlock(fn feature.FeatureFunc) (feature.AsyncFeatureAPI, error) {
	task, err := feature.NewAsyncFeature(fn)

	if err != nil {
		return nil, err
	}

	fp.statusLock.Lock()
	defer fp.statusLock.Unlock()

	if fp.statusClose {
		return nil, errors.New("pool closed")
	}

	err = fp.gp.SubmitWithoutBlock(task)

	if err != nil {
		return nil, err
	}

	return task, nil
}

func (fp *AsyncFixPool) SubmitWithTimeOut(fn feature.FeatureFunc, timeout time.Duration) (feature.AsyncFeatureAPI, error) {
	task, err := feature.NewAsyncFeature(fn)

	if err != nil {
		return nil, err
	}

	fp.statusLock.Lock()
	defer fp.statusLock.Unlock()

	if fp.statusClose {
		return nil, errors.New("pool closed")
	}

	err = fp.gp.SubmitWithTimeOut(task, timeout)

	if err != nil {
		return nil, err
	}

	return task, nil
}

func (fp *AsyncFixPool) SubmitWithContext(ctx context.Context, fn feature.FeatureFunc) (feature.AsyncFeatureAPI, error) {
	task, err := feature.NewAsyncFeature(fn)

	if err != nil {
		return nil, err
	}

	fp.statusLock.Lock()
	defer fp.statusLock.Unlock()

	if fp.statusClose {
		return nil, errors.New("pool closed")
	}

	err = fp.gp.SubmitWithContext(ctx, task)

	if err != nil {
		return nil, err
	}

	return task, nil
}
