// Package elk 提供高性能的 ELK 日志收集功能
//
// 这个包专为分布式追踪日志设计，支持异步发送日志到 Kafka，
// 可与 ELK Stack 无缝集成。主要特性包括：
//
// - 高性能异步处理
// - 分布式追踪支持 (TraceID/SpanID)
// - 灵活的配置选项
// - 并发安全设计
//
// 基本使用示例：
//
//	elkManager, err := elk.NewInstance("topic", "localhost:9092", "info")
//	if err != nil {
//		panic(err)
//	}
//
//	elkManager.Info("trace-123",
//		elk.Type("user_action"),
//		elk.TagString("user_id", "12345"),
//		elk.Data("用户登录成功"),
//	)
package elk

import (
	"net"
	"os"

	"code.iflytek.com/fengli16/pandora_elk/model"
	"code.iflytek.com/fengli16/pandora_elk/utils"
)

// NewInstance 创建 ELK 日志管理器实例
// topic: Kafka 主题名称
// brokers: Kafka broker 地址，多个地址用逗号分隔
// level: 日志级别 (trace, debug, info, warn, error)
func NewInstance(topic, brokers, level string) (*ElkLogManager, error) {
	conf := &model.ElkModel{}
	utils.FillDefaults(conf)
	conf.Kafka.Topic = topic
	conf.Kafka.Brokers = brokers
	conf.LogLevel = level

	host, _ := hostIP()
	conf.Host = host

	return initElklog(conf)
}

func hostIP() (string, error) {
	hostname, hostnameErr := os.Hostname()
	if hostnameErr != nil {
		return "", hostnameErr
	}
	addrs, err := net.LookupHost(hostname)
	if len(addrs) == 0 {
		return "", err
	}
	return addrs[0], err
}
