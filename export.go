package elk

import (
	"net"
	"os"

	"code.iflytek.com/fengli16/pandora_elk/model"
	"code.iflytek.com/fengli16/pandora_elk/utils"
)

// NewInstance 创建elk client
func NewInstance(topic, brokers, level string) (*ElkLogManager, error) {
	conf := &model.ElkModel{}
	utils.FillDefaults(conf)
	conf.Kafka.Topic = topic
	conf.Kafka.Brokers = brokers
	conf.LogLevel = level

	host, _ := hostIP()
	conf.Host = host

	return initElklog(conf)
}

func hostIP() (string, error) {
	hostname, hostnameErr := os.Hostname()
	if hostnameErr != nil {
		return "", hostnameErr
	}
	addrs, err := net.LookupHost(hostname)
	if len(addrs) == 0 {
		return "", err
	}
	return addrs[0], err
}
