package record

import (
	"code.iflytek.com/fengli16/pandora_elk/model"
)

type TraceRecord struct {
	Wrapper        WrapperTrace
	LimitArrayLen  int
	LimitStringLen int
}

func NewTraceRecord(m *model.ElkModel, tags map[string]string) *TraceRecord {
	tr := &TraceRecord{
		Wrapper: NewWrapperTrace(m),
		//MData:          map[string]any{},
		LimitArrayLen:  m.ArrayLimit,
		LimitStringLen: m.StringLimit,
	}

	if tags != nil {
		for k, v := range tags {
			tr.Wrapper.TraceBase.Tags[k] = v
		}
	}

	return tr
}

func (r *TraceRecord) TraceInfo() *TraceInfo {
	return &TraceInfo{
		TraceBase: r.Wrapper.TraceBase,
	}
}
