package record

import (
	"time"

	"code.iflytek.com/fengli16/pandora_elk/model"
)

type TraceBase struct {
	SpanId  string         `json:"spanId"`  //用户定义spanId
	TraceId string         `json:"traceId"` //用户定义traceId
	Level   string         `json:"level"`   //用户定义日志等级
	Type    string         `json:"type"`    //用户定义行为，如：http_request_failed
	Tags    map[string]any `json:"tags"`    //用户定义标签

	Timestamp int `json:"time"` //自动填写

	Action      string `json:"action"`      //配置中的servcieName
	Ip          string `json:"ip"`          //本机ip
	SubjectCode string `json:"subjectCode"` //配置中的appid

	Group  string `json:"group"`  //暂时不填
	FromId string `json:"fromId"` //暂时不填
	Port   int    `json:"port"`   //暂时不填
	Pid    int    `json:"pid"`    //暂时不填

	Uuid string `json:"uuid"` //暂时不填
}
type TraceInfo struct {
	TraceBase
	Span string `json:"span"`
	Data string `json:"data"` //用户定义日志内容
}

type MesgData struct {
	Msg any `json:"message"`
}

type MesgSpan struct {
	Msg any `json:"message"`
}

type WrapperTrace struct {
	TraceBase
	MesgData MesgData `json:"mesgData"` //用户定义日志内容
	MesgSpan MesgSpan `json:"mesgSpan"`
}

func NewTraceInfo(conf *model.ElkModel) TraceInfo {
	return TraceInfo{
		TraceBase: TraceBase{
			Timestamp: int(time.Now().UnixMilli()),
			Action:    conf.ServiceName,
			Tags:      map[string]any{},
			Ip:        conf.Host,
		},
		Data: "",
	}
}

func NewWrapperTrace(conf *model.ElkModel) WrapperTrace {
	return WrapperTrace{
		TraceBase: TraceBase{
			Timestamp: int(time.Now().UnixMilli()),
			Action:    conf.ServiceName,
			Tags:      map[string]any{},
			Ip:        conf.Host,
		},
		MesgData: MesgData{
			Msg: nil,
		},
		MesgSpan: MesgSpan{
			Msg: nil,
		},
	}
}
